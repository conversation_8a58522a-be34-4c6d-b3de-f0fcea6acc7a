<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <a-form-item name="planNo"   :label="'计划书单号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.planNo" xid="s_planNo"></a-input>
    </a-form-item>
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.contractNo" xid="s_contractNo"></a-input>
    </a-form-item>
    <a-form-item name="buyer" :label="'买家'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.buyer" id="buyer">
        <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                         :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
<!--      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear  show-search v-model:value="searchParam.buyer" id="buyer" xid="s_buyer"></cs-select>-->
    </a-form-item>
    <a-form-item name="seller" :label="'卖家'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.seller" id="seller">
        <a-select-option v-for="item in sellerOptions" :key="item.value + ' ' + item.label"
                         :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
<!--      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear  show-search v-model:value="searchParam.seller" id="seller" xid="s_seller"></cs-select>-->
    </a-form-item>
    <a-form-item name="status"   :label="'数据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search v-model:value="searchParam.status" id="status">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label" :value="item.value" :label="item.value + ' ' + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
<!--      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear  show-search v-model:value="searchParam.status" id="status" xid="s_status"></cs-select>-->
    </a-form-item>
    <a-form-item name="createBy"   :label="'创建人'" class="grid-item"  :colon="false">
      <a-input size="small" v-model:value="searchParam.createBy" xid="s_createBy"></a-input>
    </a-form-item>
    <a-form-item name="createTime"   :label="'创建时间'" class="grid-item"  :colon="false">
      <a-form-item-rest xid="s_createTime">
          <a-row>
          <a-col :span="11">
              <a-date-picker v-model:value="searchParam.createTimeFrom" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" size="small" style="width: 100%" placeholder=""/>
           </a-col>
          <a-col :span="2" style="text-align: center">-</a-col>
          <a-col :span="11">
              <a-date-picker v-model:value="searchParam.createTimeTo" valueFormat="YYYY-MM-DD" format="YYYY-MM-DD" size="small" style="width: 100%" placeholder=""/>
          </a-col>
        </a-row>
        </a-form-item-rest>
    </a-form-item>
  </a-form>
</template>
<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";
defineOptions({
  name: 'BizIEquipmentPlanHeadListSearch'
})
const searchParam = reactive({
    planNo: '',
    contractNo: '',
    buyer: '',
    seller: '',
    status: '',
    createBy: '',
    createTimeFrom:'',
    createTimeTo:'',
})

//基础资料-客商信息
const buyerOptions = reactive([])
const sellerOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.getForThirdPlanBuyerSearch}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

const getSellerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.getForThirdPlanSellerSearch}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        sellerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}


/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
  onMounted(() => {
    getBuyerOptions();
    getSellerOptions();
  })
  defineExpose({
    searchParam,
    resetSearch,
  })
</script>
<style scoped>
</style>
