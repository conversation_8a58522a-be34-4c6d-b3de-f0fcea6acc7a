<template>
  <section ref="editSectionRef">
    <a-card size="small" title="表头" class="cs-card-form head">
      <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="formRules"
              :model="formData" class="grid-container cs-form">
        <!-- 业务类型 -->
        <a-form-item name="businessType" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('businessType')"
                @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
          </template>

          <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.businessType">
            <a-select-option v-for="item in productClassify.commonBusinessType" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 合同号 -->
        <a-form-item name="contractNo" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractNo')"
                @click="handleLabelClick('contractNo')"
              >
                合同号
              </span>
          </template>
          <a-input :disabled="!addFlag" size="small" v-model:value="formData.contractNo" />
        </a-form-item>

        <!-- empty -->
        <a-form-item name="empty" class="grid-item" />

        <!-- 买家 -->
        <a-form-item name="buyer" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('buyer')"
                @click="handleLabelClick('buyer')"
              >
                买家
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.buyer">
            <a-select-option v-for="item in optionsConfig.merchantOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 卖家 -->
        <a-form-item name="seller" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('seller')"
                @click="handleLabelClick('seller')"
              >
                卖家
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.seller">
            <a-select-option v-for="item in optionsConfig.merchantOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- empty -->
        <a-form-item name="empty" class="grid-item" />

        <!-- 签约日期 -->
        <a-form-item name="signDate" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('signDate')"
                @click="handleLabelClick('signDate')"
              >
                签约日期
              </span>
          </template>
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.signDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 装运期限 -->
        <a-form-item name="shipPeriodDate" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('shipPeriodDate')"
                @click="handleLabelClick('shipPeriodDate')"
              >
                装运期限
              </span>
          </template>
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.shipPeriodDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 合同生效期 -->
        <a-form-item name="contractEffectiveDate" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractEffectiveDate')"
                @click="handleLabelClick('contractEffectiveDate')"
              >
                合同生效期
              </span>
          </template>
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.contractEffectiveDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 合同有效期 -->
        <a-form-item name="contractValidityDate" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractValidityDate')"
                @click="handleLabelClick('contractValidityDate')"
              >
                合同有效期
              </span>
          </template>
          <a-date-picker
            :disabled="showFlag" :valueFormat="DATE_FORMAT.DATE" :format="DATE_FORMAT.DATE" :locale="zhCN"
            :placeholder="EMPTY.STRING" size="small" v-model:value="formData.contractValidityDate" style="width: 100%"
          />
        </a-form-item>

        <!-- 签约地点(中文) -->
        <a-form-item name="signPlaceCn" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('signPlaceCn')"
                @click="handleLabelClick('signPlaceCn')"
              >
                签约地点(中文)
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.signPlaceCn">
            <a-select-option v-for="item in optionsConfig.cityOptions" :key="`${item.value}`"
                             :value="item.value" :label="`${item.label}`">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 签约地点(英文) -->
        <a-form-item name="signPlaceEn" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('signPlaceEn')"
                @click="handleLabelClick('signPlaceEn')"
              >
                签约地点(英文)
              </span>
          </template>
          <a-input :disabled="showFlag" size="small" v-model:value="formData.signPlaceEn" maxlength="100" />
        </a-form-item>

        <!-- 装运港 -->
        <a-form-item name="shippingPort" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('shippingPort')"
                @click="handleLabelClick('shippingPort')"
              >
                装运港
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.shippingPort">
            <a-select-option v-for="item in optionsConfig.portOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 目的港 -->
        <a-form-item name="destPort" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('destPort')"
                @click="handleLabelClick('destPort')"
              >
                目的港
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.destPort">
            <a-select-option v-for="item in optionsConfig.portOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 收汇方式 -->
        <a-form-item name="paymentMethod" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('paymentMethod')"
                @click="handleLabelClick('paymentMethod')"
              >
                收汇方式
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.paymentMethod">
            <a-select-option v-for="item in productClassify.paymentMethod" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 币种 -->
        <a-form-item name="curr" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('curr')"
                @click="handleLabelClick('curr')"
              >
                币种
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.curr">
            <a-select-option v-for="item in optionsConfig.currOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 价格条款 -->
        <a-form-item name="priceTerm" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('priceTerm')"
                @click="handleLabelClick('priceTerm')"
              >
                价格条款
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.priceTerm">
            <a-select-option v-for="item in optionsConfig.priceTermsOptions" :key="`${item.value}`"
                             :value="item.value" :label="`${item.label}`">
              {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 价格条款对应港口 -->
        <a-form-item name="priceTermPort" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('priceTermPort')"
                @click="handleLabelClick('priceTermPort')"
              >
                价格条款对应港口
              </span>
          </template>
          <cs-select :disabled="showFlag" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.priceTermPort">
            <a-select-option v-for="item in productClassify.priceTermPort" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 建议授权签约人 -->
        <a-form-item name="suggestAuthorSignatory" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('suggestAuthorSignatory')"
                @click="handleLabelClick('suggestAuthorSignatory')"
              >
                建议授权签约人
              </span>
          </template>
          <a-input :disabled="showFlag" size="small" v-model:value="formData.suggestAuthorSignatory" maxlength="50" />
        </a-form-item>

        <!-- 短溢数 -->
        <a-form-item name="shortOverflowNumber" class="grid-item short-number" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('shortOverflowNumber')"
                @click="handleLabelClick('shortOverflowNumber')"
              >
                短溢数
              </span>
          </template>
          <a-input-number :disabled="showFlag" size="small" v-model:value="formData.shortOverflowNumber"
                          :addon-after="' % '" style="width: 100%" :precision="4" :controls="false" />
        </a-form-item>

        <!-- 备注 -->
        <a-form-item name="note" class="grid-item merge-3" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('note')"
                @click="handleLabelClick('note')"
              >
                备注
              </span>
          </template>
          <a-textarea :disabled="showFlag" size="small" v-model:value="formData.note"
                      :auto-size="{ minRows: 2, maxRows:  3}" />
        </a-form-item>

        <!-- 制单人 -->
        <a-form-item name="documentMaker" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('documentMaker')"
                @click="handleLabelClick('documentMaker')"
              >
                制单人
              </span>
          </template>
          <a-input disabled size="small" v-model:value="formData.documentMaker" />
        </a-form-item>

        <!-- 制单时间 -->
        <a-form-item name="documentMakeDate" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('documentMakeDate')"
                @click="handleLabelClick('documentMakeDate')"
              >
                制单时间
              </span>
          </template>
          <a-date-picker disabled v-model:value="formData.documentMakeDate" :valueFormat="DATE_FORMAT.DATE_TIME"
                         :format="DATE_FORMAT.DATE_TIME"
                         :locale="zhCN" :placeholder="EMPTY.STRING" size="small" style="width: 100%" />
        </a-form-item>

        <!-- 单据状态 -->
        <a-form-item name="dataStatus" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('dataStatus')"
                @click="handleLabelClick('dataStatus')"
              >
                单据状态
              </span>
          </template>
          <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.dataStatus">
            <a-select-option v-for="item in productClassify.data_status" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 确认时间 -->
        <a-form-item name="confirmTime" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('confirmTime')"
                @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
          </template>
          <a-date-picker
            disabled
            v-model:value="formData.confirmTime"
            :valueFormat="DATE_FORMAT.DATE_TIME"
            :format="DATE_FORMAT.DATE_TIME"
            :locale="zhCN"
            placeholder=""
            size="small"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 审批状态 -->
        <a-form-item name="apprStatus" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('apprStatus')"
                @click="handleLabelClick('apprStatus')"
              >
                审批状态
              </span>
          </template>
          <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.apprStatus">
            <a-select-option v-for="item in productClassify.approval_status" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 版本号 -->
        <a-form-item name="versionNo" class="grid-item" :colon="false">
          <template #label>
              <span
                class="form-label"
                :class="getLabelClass('versionNo')"
                @click="handleLabelClick('versionNo')"
              >
                版本号
              </span>
          </template>
          <a-input disabled size="small" v-model:value="formData.versionNo" />
        </a-form-item>

        <div class="cs-submit-btn merge-3">
          <a-button size="small" @click="edit.back(true)">
            返回
          </a-button>
          <a-button v-if="showFlag" size="small" :loading="passLoading" @click="handlePass" class="cs-margin-right">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue" />
            </template>
            审核通过
          </a-button>
          <a-button v-if="showFlag" size="small" :loading="returnLoading" @click="handleReturn" class="cs-margin-right">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red" />
            </template>
            审核退回
          </a-button>
        </div>
      </a-form>
    </a-card>

    <a-card title="表体" size="small" class="cs-card-form body">
      <div class="switch-tab">
        <a-button type="text" :class="bodyClasses.process" @click="updateCurrentTab('process')">出料加工</a-button>
        <a-button type="text" :class="bodyClasses.slice" @click="updateCurrentTab('slice')">进口薄片</a-button>
      </div>

      <div v-show="bodyTabs.process">
        <seven-foreign-contract-body-list ref="processBodyListRef" body-type="process" />
      </div>
      <div v-show="bodyTabs.slice">
        <seven-foreign-contract-body-list ref="sliceBodyListRef" body-type="slice" />
      </div>
    </a-card>
  </section>
</template>

<script lang="jsx" setup>
import CsSelect from '@/components/select/CsSelect.vue'
import SevenForeignContractBodyList from '@/view/seven/foreignContract/body/SevenForeignContractBodyList'
import { ref, inject, createVNode, watch, onMounted } from 'vue'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { message, Modal } from 'ant-design-vue'
import { productClassify, DATE_FORMAT, DATA_STATUS, EMPTY } from '@/view/common/constant'
import { EDIT, OPTIONS_CONFIG } from '@/view/seven/foreignContract/js/handle'
import { useFieldMarking } from '@/utils/useFieldMarking'
import ycCsApi from '@/api/ycCsApi'
import fieldNameMap from '@/view/seven/foreignContract/audit/map'

const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()

const edit = inject(EDIT)
const optionsConfig = inject(OPTIONS_CONFIG)

// 通用显示标识
const showFlag = edit.commonShowFlag
// 通用新增标识
const addFlag = edit.commonAddFlag

// 表头form ref
const formRef = ref(null)

// 表体ref
const processBodyListRef = ref(null)
const sliceBodyListRef = ref(null)

// 表头form数据
const formData = ref({
  id: '',
  businessType: '',
  contractNo: '',
  buyer: '',
  seller: '',
  signDate: '',
  shipPeriodDate: '',
  signPlaceCn: '',
  signPlaceEn: '',
  contractEffectiveDate: undefined,
  contractValidityDate: undefined,
  shippingPort: '',
  destPort: '',
  paymentMethod: '',
  curr: '',
  priceTerm: '',
  priceTermPort: '',
  suggestAuthorSignatory: '',
  shortOverflowNumber: undefined,
  note: '',
  versionNo: '',
  documentMaker: '',
  documentMakeDate: undefined,
  dataStatus: '',
  confirmTime: undefined,
  apprStatus: ''
})

// 表头form校验规则
const formRules = {
  businessType: [
    { required: true, message: '请输入业务类型', trigger: 'blur' }
  ],
  contractNo: [
    { required: true, message: '请输入合同号', trigger: 'blur' }
  ],
  buyer: [
    { required: true, message: '请选择买家', trigger: 'change' }
  ],
  seller: [
    { required: true, message: '请选择卖家', trigger: 'change' }
  ],
  curr: [
    { required: true, message: '请选择币种', trigger: 'change' }
  ],
  versionNo: [
    { required: true, message: '请输入版本号', trigger: 'change' }
  ],
  dataStatus: [
    { required: true, message: '请选择数据状态', trigger: 'change' }
  ]
}

watch(() => formData.value.signPlaceCn, cityCnName => {
  if (!cityCnName) {
    return
  }
  const currentOption = optionsConfig.value.cityOptions.filter(city => city.value === cityCnName)
  if (currentOption && currentOption.length && currentOption.length > 0) {
    formData.value.signPlaceEn = currentOption[0].enName
  }
})

const bodyTabs = ref({
  process: true,
  slice: false
})

const bodyClasses = ref({
  process: ['tab-button', 'active'],
  slice: ['tab-button', 'inactive']
})

let displayTimer = null

const editSectionRef = ref(null)

async function updateCurrentTab(tab) {
  editSectionRef.value.style.setProperty('--scroll-display', 'none')
  try {
    Object.keys(bodyTabs.value).forEach(key => {
      bodyTabs.value[key] = false
    })
    Object.keys(bodyClasses.value).forEach(key => {
      bodyClasses.value[key] = bodyClasses.value[key].filter(c => c !== 'active')
      bodyClasses.value[key].push('inactive')
    })
    bodyTabs.value[tab] = true
    bodyClasses.value[tab] = bodyClasses.value[tab].filter(c => c !== 'inactive')
    bodyClasses.value[tab].push('active')
  } finally {
    clearTimeout(displayTimer)
    displayTimer = setTimeout(() => {
      editSectionRef.value.style.setProperty('--scroll-display', 'initial')
    }, 200)
  }
}

const passLoading = ref(false)

function handlePass() {
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }
  passLoading.value = true
  try {
    const auditOpinion = ref('同意审批')
    Modal.confirm({
      title: '审核通过',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: () => {
        return (
          <div>
            <div style={{ marginTop: '10px' }}>
              <label style={{ display: 'block', marginBottom: '5px' }}>
                审核意见：
              </label>
              <textarea value={auditOpinion.value}
                        onInput={e => auditOpinion.value = e.target.value}
                        style={{
                          width: '100%',
                          height: '80px',
                          padding: '8px',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          resize: 'vertical'
                        }}
                        placeholder="请输入审核意见">
            </textarea>
            </div>
          </div>
        )
      },
      async onOk() {
        passLoading.value = true
        try {
          const params = {
            ids: [formData.value.id],
            apprMessage: auditOpinion.value || '同意审批',
            businessType: '7',
            billType: 'contract'
          }
          const res = await window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.audit, params)
          if (res['code'] === 200) {
            edit.back(true)
            message.success('审核通过成功')
          } else {
            message.error(res['message'] || '审核失败')
          }
        } catch (error) {
          console.error('审核通过失败:', error)
          message.error('审核通过失败，请重试')
        } finally {
          passLoading.value = false
        }
      }
    })
  } finally {
    passLoading.value = false
  }
}

const returnLoading = ref(false)

function handleReturn() {
  returnLoading.value = true
  try {
    // 直接读取当前页面的标记信息
    let markedFields = ''
    const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
    if (redFieldNames.length > 0) {
      // 将英文字段名转换为中文显示
      const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
      markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
    }
    // 审核意见输入框
    const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')
    // 弹出审核退回确认框
    Modal.confirm({
      title: '审核退回',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确认',
      cancelText: '取消',
      content: () => {
        return (
          <div>
            <div style={{ marginTop: '10px' }}>
              <label style={{ display: 'block', marginBottom: '5px' }}>
                审核意见：
              </label>
              <textarea value={auditOpinion.value}
                        onInput={e => auditOpinion.value = e.target.value}
                        style={{
                          width: '100%',
                          height: '80px',
                          padding: '8px',
                          border: '1px solid #d9d9d9',
                          borderRadius: '4px',
                          resize: 'vertical'
                        }}
                        placeholder="请输入审核意见">
            </textarea>
            </div>
          </div>
        )
      },
      async onOk() {
        returnLoading.value = true
        try {
          const params = {
            ids: [formData.value.id],
            apprMessage: auditOpinion.value || '审批退回',
            businessType: '7',
            billType: 'contract'
          }
          const res = await window.majesty.httpUtil.postAction(ycCsApi.approvalFlow.reject, params)
          if (res['code'] === 200) {
            await saveCurrentMarkings(formData.value.id, 'default', fieldMarkings.value)
            edit.back(true)
            message.success('审核退回成功')
          } else {
            message.error(res['message'])
          }
        } catch (error) {
          console.error('审核退回失败:', error)
          message.error('审核退回失败，请重试')
        } finally {
          returnLoading.value = false
        }
      }
    })
  } finally {
    returnLoading.value = false
  }
}

/**
 * 初始化表单数据
 */
function initFormData() {
  if (addFlag.value) {
    formData.value.businessType = '7'
    formData.value.curr = 'USD'
    formData.value.priceTermPort = '1'
    formData.value.versionNo = '1'
    formData.value.dataStatus = DATA_STATUS.DRAFT
    formData.value.apprStatus = '0'
    return
  }
  const editConfig = edit.config.value
  formData.value = Object.assign(formData.value, editConfig['editData'])
  formData.value.id = editConfig['headId']
}

onMounted(() => {
  initFormData()
})

defineOptions({
  name: 'SevenForeignContractAuditEdit'
})
</script>

<style scoped>
:deep(.cs-submit-btn) {
  padding-bottom: 0;
  margin: 10px 0
}

.head :deep(.ant-card-body) {
  padding-bottom: 0;
}

.body :deep(.ant-card-body) {
  padding-top: 0;
}

.switch-tab {
  margin-top: 15px;
}

.tab-button {
  background-color: #fff;
  border-radius: initial;
}

.tab-button.active {
  border-color: rgb(217, 217, 217);
  color: #1677ff;
  text-shadow: 0 0 0.25px currentColor;
  background-color: initial;
  border-radius: initial;
}

.tab-button.inactive {
  opacity: 0.65;

  &:hover {
    opacity: initial;
  }
}

:deep(.surely-table-horizontal-scroll) {
  display: var(--scroll-display, initial);
}

:deep(.short-number .ant-input-number-input-wrap input) {
  font-size: 12px;
}

.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
